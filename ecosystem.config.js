// =============================================================================
// TOOLRAPTER - PM2 ECOSYSTEM CONFIGURATION
// =============================================================================
// Enterprise-grade PM2 configuration for Hostinger VPS deployment
// Domain: toolrapter.com | VPS: ************

module.exports = {
  apps: [
    {
      // Application Configuration
      name: 'toolrapter',
      script: 'npm',
      args: 'start',
      cwd: '/var/www/toolrapter',
      
      // Process Management
      instances: 'max', // Use all available CPU cores
      exec_mode: 'cluster',
      
      // Auto-restart Configuration
      autorestart: true,
      watch: false, // Disable in production
      max_memory_restart: '1G',
      
      // Environment Variables
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        NEXT_TELEMETRY_DISABLED: 1,
      },
      
      // Logging Configuration
      log_file: '/var/log/pm2/toolrapter.log',
      out_file: '/var/log/pm2/toolrapter-out.log',
      error_file: '/var/log/pm2/toolrapter-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Advanced Options
      kill_timeout: 5000,
      listen_timeout: 10000,
      shutdown_with_message: true,
      
      // Health Monitoring
      min_uptime: '10s',
      max_restarts: 10,
      
      // Source Control
      source_map_support: true,
      
      // Performance Monitoring
      pmx: true,
      
      // Custom Environment for Staging
      env_staging: {
        NODE_ENV: 'production',
        PORT: 3001,
        NEXT_TELEMETRY_DISABLED: 1,
      },
      
      // Custom Environment for Development
      env_development: {
        NODE_ENV: 'development',
        PORT: 3000,
        NEXT_TELEMETRY_DISABLED: 1,
        watch: true,
        ignore_watch: [
          'node_modules',
          '.next',
          'logs',
          '*.log'
        ],
      }
    }
  ],
  
  // Deployment Configuration
  deploy: {
    production: {
      user: 'root',
      host: ['************'],
      ref: 'origin/main',
      repo: 'https://github.com/MuhammadShahbaz195/ToolCrush.git',
      path: '/var/www/toolrapter',
      'pre-deploy-local': '',
      'post-deploy': 'npm ci --production && npm run download-fonts && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    },

    staging: {
      user: 'root',
      host: ['************'],
      ref: 'origin/develop',
      repo: 'https://github.com/MuhammadShahbaz195/ToolCrush.git',
      path: '/var/www/toolrapter-staging',
      'pre-deploy-local': '',
      'post-deploy': 'npm ci --production && npm run download-fonts && npm run build && pm2 reload ecosystem.config.js --env staging',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    }
  }
};

// =============================================================================
// PM2 COMMANDS REFERENCE
// =============================================================================
/*

# Start the application
pm2 start ecosystem.config.js --env production

# Monitor applications
pm2 monit

# View logs
pm2 logs toolrapter

# Restart application
pm2 restart toolrapter

# Stop application
pm2 stop toolrapter

# Delete application
pm2 delete toolrapter

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
pm2 startup

# Deploy from local machine
pm2 deploy production setup
pm2 deploy production

# Update application
pm2 deploy production update

# Revert deployment
pm2 deploy production revert 1

# Show application info
pm2 show toolrapter

# Reload application (zero-downtime)
pm2 reload toolrapter

# Scale application
pm2 scale toolrapter 4

*/
