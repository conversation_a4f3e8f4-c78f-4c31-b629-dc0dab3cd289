# =============================================================================
# TOOLCRUSH - VPS DEPLOYMENT WORKFLOW
# =============================================================================
# GitHub Actions workflow for deploying to Hostinger VPS with PM2 and Nginx

name: 🖥️ Deploy to VPS

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'production'
        type: choice
        options:
          - staging
          - production
      force_deploy:
        description: 'Force deployment (skip checks)'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18'
  APP_NAME: 'toolcrush'

jobs:
  # =============================================================================
  # PRE-DEPLOYMENT CHECKS
  # =============================================================================
  pre-deploy:
    name: 🔍 Pre-deployment Checks
    runs-on: ubuntu-latest
    if: ${{ !inputs.force_deploy }}

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: |
          npm ci --prefer-offline --no-audit
          npm run download-fonts

      - name: 🔧 Type check
        run: npx tsc --noEmit

      - name: 🧪 Run tests
        run: npm test

      - name: 🏗️ Test build
        run: npm run build
        env:
          NODE_ENV: production

  # =============================================================================
  # VPS DEPLOYMENT
  # =============================================================================
  deploy:
    name: 🚀 Deploy to VPS
    runs-on: ubuntu-latest
    needs: pre-deploy
    if: always() && (needs.pre-deploy.result == 'success' || inputs.force_deploy)
    environment: ${{ inputs.environment }}

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: |
          npm ci --prefer-offline --no-audit --production=false
          npm run download-fonts

      - name: 🏗️ Build application
        run: npm run build
        env:
          NODE_ENV: production
          NEXT_TELEMETRY_DISABLED: 1

      - name: 📦 Create deployment package
        run: |
          tar -czf deployment.tar.gz \
            .next/ \
            public/ \
            package.json \
            package-lock.json \
            next.config.js \
            scripts/ \
            --exclude=node_modules

      - name: 🔐 Setup SSH key
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.VPS_SSH_KEY }}

      - name: 📤 Upload to VPS
        run: |
          scp -o StrictHostKeyChecking=no \
            deployment.tar.gz \
            ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }}:/tmp/

      - name: 🚀 Deploy on VPS
        run: |
          ssh -o StrictHostKeyChecking=no \
            ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} << 'EOF'
          
          # Set variables
          APP_DIR="/var/www/${{ env.APP_NAME }}"
          BACKUP_DIR="/var/backups/${{ env.APP_NAME }}"
          
          # Create backup of current deployment
          if [ -d "$APP_DIR" ]; then
            echo "📦 Creating backup..."
            sudo mkdir -p "$BACKUP_DIR"
            sudo cp -r "$APP_DIR" "$BACKUP_DIR/backup-$(date +%Y%m%d-%H%M%S)"
          fi
          
          # Prepare deployment directory
          sudo mkdir -p "$APP_DIR"
          cd "$APP_DIR"
          
          # Extract new deployment
          echo "📤 Extracting deployment package..."
          sudo tar -xzf /tmp/deployment.tar.gz -C "$APP_DIR"
          sudo chown -R ${{ secrets.VPS_USER }}:${{ secrets.VPS_USER }} "$APP_DIR"
          
          # Install production dependencies
          echo "📥 Installing dependencies..."
          npm ci --production --prefer-offline
          
          # Download fonts
          npm run download-fonts
          
          # Restart application with PM2
          echo "🔄 Restarting application..."
          pm2 stop ${{ env.APP_NAME }} || true
          pm2 delete ${{ env.APP_NAME }} || true
          pm2 start npm --name "${{ env.APP_NAME }}" -- start
          pm2 save
          
          # Reload Nginx
          echo "🔄 Reloading Nginx..."
          sudo nginx -t && sudo systemctl reload nginx
          
          # Health check
          echo "🏥 Performing health check..."
          sleep 10
          if curl -f http://localhost:3000/api/health; then
            echo "✅ Deployment successful!"
          else
            echo "❌ Health check failed!"
            exit 1
          fi
          
          # Cleanup
          rm -f /tmp/deployment.tar.gz
          
          EOF

      - name: 🧹 Cleanup local files
        if: always()
        run: rm -f deployment.tar.gz

  # =============================================================================
  # POST-DEPLOYMENT VERIFICATION
  # =============================================================================
  verify:
    name: ✅ Verify Deployment
    runs-on: ubuntu-latest
    needs: deploy
    if: success()

    steps:
      - name: 🏥 Health check
        run: |
          echo "🏥 Performing external health check..."
          
          # Wait for application to be ready
          sleep 30
          
          # Check if application is responding
          if curl -f --max-time 30 https://${{ secrets.VPS_DOMAIN }}/api/health; then
            echo "✅ Application is healthy!"
          else
            echo "❌ Application health check failed!"
            exit 1
          fi

      - name: 🔍 Performance check
        run: |
          echo "📊 Running basic performance check..."
          
          # Check response time
          RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' https://${{ secrets.VPS_DOMAIN }})
          echo "⏱️ Response time: ${RESPONSE_TIME}s"
          
          # Fail if response time is too slow (> 5 seconds)
          if (( $(echo "$RESPONSE_TIME > 5.0" | bc -l) )); then
            echo "❌ Response time too slow!"
            exit 1
          fi

      - name: 📢 Deployment notification
        if: success()
        run: |
          echo "🎉 VPS deployment completed successfully!"
          echo "🌐 Application is live at: https://${{ secrets.VPS_DOMAIN }}"
          echo "📊 Environment: ${{ inputs.environment }}"

  # =============================================================================
  # ROLLBACK (MANUAL TRIGGER)
  # =============================================================================
  rollback:
    name: ⏪ Rollback Deployment
    runs-on: ubuntu-latest
    if: failure() && needs.deploy.result == 'failure'
    needs: [deploy, verify]

    steps:
      - name: 🔐 Setup SSH key
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.VPS_SSH_KEY }}

      - name: ⏪ Rollback to previous version
        run: |
          ssh -o StrictHostKeyChecking=no \
            ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} << 'EOF'
          
          APP_DIR="/var/www/${{ env.APP_NAME }}"
          BACKUP_DIR="/var/backups/${{ env.APP_NAME }}"
          
          # Find latest backup
          LATEST_BACKUP=$(ls -t "$BACKUP_DIR" | head -n1)
          
          if [ -n "$LATEST_BACKUP" ]; then
            echo "⏪ Rolling back to: $LATEST_BACKUP"
            
            # Stop current application
            pm2 stop ${{ env.APP_NAME }} || true
            
            # Restore from backup
            sudo rm -rf "$APP_DIR"
            sudo cp -r "$BACKUP_DIR/$LATEST_BACKUP" "$APP_DIR"
            sudo chown -R ${{ secrets.VPS_USER }}:${{ secrets.VPS_USER }} "$APP_DIR"
            
            # Restart application
            cd "$APP_DIR"
            pm2 start npm --name "${{ env.APP_NAME }}" -- start
            pm2 save
            
            echo "✅ Rollback completed!"
          else
            echo "❌ No backup found for rollback!"
            exit 1
          fi
          
          EOF

      - name: 📢 Rollback notification
        if: success()
        run: |
          echo "⏪ Rollback completed successfully!"
          echo "🔄 Application restored to previous version"
