# =============================================================================
# TOOLCRUSH - PRODUCTION-READY ENVIRONMENT CONFIGURATION
# =============================================================================
# Enterprise-grade Next.js 14 application environment setup
# Copy this file to .env.local and fill in your actual values
#
# 🔒 SECURITY WARNING: Never commit this file with real values to version control
# 🚀 For production deployment, use your hosting platform's environment variable system

# =============================================================================
# CORE APPLICATION SETTINGS
# =============================================================================

# Environment
NODE_ENV=development
NEXT_PUBLIC_BASE_URL=http://localhost:3000

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================

# NextAuth.js Configuration
NEXTAUTH_SECRET=your-super-secure-secret-key-minimum-32-characters
NEXTAUTH_URL=http://localhost:3000

# Google OAuth (for authentication)
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# MongoDB Connection
MONGODB_URI=mongodb+srv://username:<EMAIL>/toolbox?retryWrites=true&w=majority

# =============================================================================
# RATE LIMITING & SECURITY
# =============================================================================

# Upstash Redis for Rate Limiting (Production)
UPSTASH_REDIS_REST_URL=https://your-redis-instance.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-upstash-redis-token

# Security Configuration
CSRF_SECRET=your-csrf-secret-key-minimum-32-characters

# =============================================================================
# FILE STORAGE & MEDIA
# =============================================================================

# Cloudinary Configuration (for image uploads)
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# =============================================================================
# EMAIL CONFIGURATION (Optional)
# =============================================================================

# SMTP Configuration for contact forms
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>

# =============================================================================
# ANALYTICS & MONITORING (Optional)
# =============================================================================

# Google Analytics
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# Performance Monitoring
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Debug Settings (Development Only)
DEBUG_MODE=false
VERBOSE_LOGGING=false

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================

# Production Security Headers
ENABLE_STRICT_CSP=true
ENABLE_HSTS=true
ENABLE_SECURITY_HEADERS=true

# Performance Settings
ENABLE_COMPRESSION=true
ENABLE_CACHING=true
CACHE_TTL=300

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/Disable Features
ENABLE_CONTACT_FORM=true
ENABLE_BLOG_SYSTEM=true
ENABLE_USER_REGISTRATION=true
ENABLE_ADMIN_PANEL=true
ENABLE_PDF_TOOLS=true
ENABLE_CALCULATORS=true

# =============================================================================
# API CONFIGURATION
# =============================================================================

# External API Keys (if needed)
EXTERNAL_API_KEY=your-external-api-key
EXTERNAL_API_SECRET=your-external-api-secret

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# Deployment Platform (hostinger, aws, digitalocean, etc.)
DEPLOYMENT_PLATFORM=hostinger

# Domain Configuration
NEXT_PUBLIC_DOMAIN=toolcrush.com
NEXT_PUBLIC_APP_NAME=ToolCrush

# CDN Configuration (if using external CDN)
NEXT_PUBLIC_CDN_URL=https://cdn.toolcrush.com

# =============================================================================
# MONITORING & LOGGING
# =============================================================================

# Application Monitoring
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ORG=your-sentry-org
SENTRY_PROJECT=toolcrush

# Performance Monitoring
NEXT_PUBLIC_ANALYTICS_ID=your-analytics-id

# Error Tracking
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true

# =============================================================================
# BACKUP & MAINTENANCE
# =============================================================================

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=We're currently performing scheduled maintenance. Please check back soon.

# =============================================================================
# DEPLOYMENT-SPECIFIC CONFIGURATIONS
# =============================================================================

# Production URL Configuration
NEXT_PUBLIC_PRODUCTION_URL=https://toolrapter.com
NEXT_PUBLIC_APP_ENV=production

# Hostinger VPS Configuration
VPS_HOST=your-server-ip
VPS_PORT=3000
PM2_APP_NAME=toolcrush

# SSL Configuration (for VPS deployment)
SSL_CERT_PATH=/etc/letsencrypt/live/toolcrush.com/fullchain.pem
SSL_KEY_PATH=/etc/letsencrypt/live/toolcrush.com/privkey.pem

# =============================================================================
# ADVANCED SECURITY SETTINGS
# =============================================================================

# JWT Configuration
JWT_ALGORITHM=HS256
JWT_ISSUER=toolcrush.com
JWT_AUDIENCE=toolcrush-users

# Session Configuration
SESSION_TIMEOUT=1800
SESSION_ABSOLUTE_TIMEOUT=86400

# Password Policy
MIN_PASSWORD_LENGTH=8
REQUIRE_SPECIAL_CHARS=true
REQUIRE_NUMBERS=true

# =============================================================================
# QUICK SETUP GUIDE
# =============================================================================

# 1. REQUIRED FOR BASIC FUNCTIONALITY:
#    - NEXTAUTH_SECRET (generate with: openssl rand -base64 32)
#    - MONGODB_URI (from MongoDB Atlas or local instance)
#    - NEXTAUTH_URL (your application URL)

# 2. REQUIRED FOR PRODUCTION:
#    - UPSTASH_REDIS_REST_URL and TOKEN (for rate limiting)
#    - GOOGLE_CLIENT_ID and SECRET (for OAuth)
#    - Strong CSRF_SECRET

# 3. OPTIONAL BUT RECOMMENDED:
#    - CLOUDINARY_* (for image uploads)
#    - SMTP_* (for email functionality)
#    - SENTRY_DSN (for error tracking)
#    - GA_ID (for analytics)

# 4. DEPLOYMENT CHECKLIST:
#    ✅ Set NODE_ENV=production
#    ✅ Use HTTPS URLs for all external services
#    ✅ Enable security headers (ENABLE_STRICT_CSP=true)
#    ✅ Configure rate limiting with Redis
#    ✅ Set up monitoring and logging
#    ✅ Configure backup strategy
#    ✅ Test all integrations

# =============================================================================
# SECURITY REMINDERS
# =============================================================================

# 🔒 NEVER commit this file with real values to version control
# 🔑 Use strong, unique passwords and secrets (minimum 32 characters)
# 🔄 Rotate secrets regularly in production (quarterly recommended)
# 🌍 Use environment-specific values for each deployment
# 📊 Monitor for unusual activity and failed authentication attempts
# 🛡️ Keep dependencies updated and scan for vulnerabilities
# 🔐 Use HTTPS in production and secure cookie settings
# 📝 Document any custom environment variables for your team

# =============================================================================
# ENVIRONMENT EXAMPLES
# =============================================================================

# Development:
# NODE_ENV=development
# NEXT_PUBLIC_BASE_URL=http://localhost:3000
# NEXTAUTH_URL=http://localhost:3000

# Staging:
# NODE_ENV=production
# NEXT_PUBLIC_BASE_URL=https://staging.toolcrush.com
# NEXTAUTH_URL=https://staging.toolcrush.com

# Production:
# NODE_ENV=production
# NEXT_PUBLIC_BASE_URL=https://toolcrush.com
# NEXTAUTH_URL=https://toolcrush.com
