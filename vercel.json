{"version": 2, "name": "toolcrush", "alias": ["toolcrush.com", "www.toolcrush.com"], "regions": ["iad1", "sfo1"], "framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm ci && npm run download-fonts", "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}, {"source": "/_next/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/images/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000"}]}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}, {"source": "/blogs", "destination": "/blog", "permanent": true}, {"source": "/tools/pdf-converter", "destination": "/tools/pdf-to-word", "permanent": false}], "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap"}, {"source": "/robots.txt", "destination": "/api/robots"}], "env": {"NEXT_TELEMETRY_DISABLED": "1"}, "build": {"env": {"NODE_ENV": "production", "NEXT_TELEMETRY_DISABLED": "1"}}, "crons": [{"path": "/api/cron/cleanup", "schedule": "0 2 * * *"}, {"path": "/api/cron/sitemap", "schedule": "0 6 * * *"}]}