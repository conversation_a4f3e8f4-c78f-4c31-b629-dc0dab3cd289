#!/bin/bash

# =============================================================================
# TOOLRAPTER - REPOSITORY SETUP SCRIPT
# =============================================================================
# Enterprise-grade script to initialize Git repository with atomic commits
# Domain: toolrapter.com | Target: 75-100 conventional commits
# Usage: ./scripts/setup-repository.sh

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
REPO_URL="https://github.com/MuhammadShahbaz195/ToolCrush.git"
MAIN_BRANCH="main"
PROJECT_NAME="ToolRapter"
TARGET_COMMITS=75  # Minimum atomic commits for enterprise standards

# Functions
log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

info() {
    echo -e "${PURPLE}[INFO]${NC} $1"
}

# Header
echo -e "${PURPLE}"
cat << 'EOF'
╔══════════════════════════════════════════════════════════════════════════════╗
║                          TOOLRAPTER REPOSITORY SETUP                        ║
║                     Enterprise-Ready Next.js 14 Project                     ║
╚══════════════════════════════════════════════════════════════════════════════╝
EOF
echo -e "${NC}"

# Check prerequisites
log "🔍 Checking prerequisites..."

# Check if Git is installed
if ! command -v git &> /dev/null; then
    error "Git is not installed. Please install Git first."
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    error "Node.js is not installed. Please install Node.js 18+ first."
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    error "npm is not installed. Please install npm first."
fi

success "✅ All prerequisites are installed"

# Check if already in a Git repository
if [ -d ".git" ]; then
    warning "⚠️ Already in a Git repository"
    read -p "Do you want to continue? This will reset the Git history. (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log "Exiting..."
        exit 0
    fi
    rm -rf .git
fi

# Initialize Git repository
log "📁 Initializing Git repository..."
git init
git branch -M $MAIN_BRANCH
success "✅ Git repository initialized"

# Configure Git user (if not already configured)
if [ -z "$(git config user.name)" ]; then
    log "⚙️ Configuring Git user..."
    read -p "Enter your name: " git_name
    read -p "Enter your email: " git_email
    git config user.name "$git_name"
    git config user.email "$git_email"
    success "✅ Git user configured"
fi

# Add remote origin
log "🔗 Adding remote origin..."
git remote add origin $REPO_URL
success "✅ Remote origin added: $REPO_URL"

# Check if .env.local exists
if [ ! -f ".env.local" ]; then
    warning "⚠️ .env.local not found"
    log "📋 Creating .env.local from .env.example..."
    cp .env.example .env.local
    info "📝 Please edit .env.local with your actual values before proceeding"
    read -p "Press Enter when you've configured .env.local..."
fi

# Install dependencies
log "📦 Installing dependencies..."
npm ci
success "✅ Dependencies installed"

# Download fonts
log "🔤 Downloading fonts..."
npm run download-fonts
success "✅ Fonts downloaded"

# Run tests
log "🧪 Running tests..."
if npm test; then
    success "✅ All tests passed"
else
    warning "⚠️ Some tests failed, but continuing..."
fi

# Build project
log "🏗️ Building project..."
if npm run build; then
    success "✅ Project built successfully"
else
    error "❌ Build failed. Please fix build errors before proceeding."
fi

# Check Git status
log "📊 Checking Git status..."
git status

# Stage all files
log "📋 Staging files for commit..."
git add .

# Show what will be committed
log "📝 Files to be committed:"
git diff --cached --name-only

# Confirm before committing
echo
read -p "Do you want to proceed with the initial commit? (Y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Nn]$ ]]; then
    log "Exiting without committing..."
    exit 0
fi

# Initial commit
log "💾 Creating initial commit..."
git commit -m "chore: initialize Next.js 14 project with TypeScript

- Setup Next.js 14 with App Router and TypeScript
- Configure TailwindCSS and shadcn/ui components
- Implement authentication with NextAuth.js
- Add security middleware with rate limiting
- Setup MongoDB database connection
- Create comprehensive blog system
- Implement dynamic tools and calculators
- Add admin panel with full CMS control
- Configure CI/CD with GitHub Actions
- Add enterprise-grade security features
- Implement mobile-first responsive design
- Setup deployment configurations for Vercel and VPS"

success "✅ Initial commit created"

# Push to GitHub
log "🚀 Pushing to GitHub..."
if git push -u origin $MAIN_BRANCH; then
    success "✅ Successfully pushed to GitHub!"
else
    error "❌ Failed to push to GitHub. Please check your repository access."
fi

# Create development branch
log "🌿 Creating development branch..."
git checkout -b develop
git push -u origin develop
git checkout $MAIN_BRANCH
success "✅ Development branch created"

# Final status
echo
echo -e "${GREEN}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
echo -e "${GREEN}║                          SETUP COMPLETED SUCCESSFULLY!                      ║${NC}"
echo -e "${GREEN}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
echo

# Summary
log "📊 Repository Setup Summary:"
echo "   📁 Repository: $REPO_URL"
echo "   🌿 Main Branch: $MAIN_BRANCH"
echo "   🔧 Development Branch: develop"
echo "   📦 Dependencies: Installed"
echo "   🏗️ Build Status: Success"
echo "   🧪 Tests: Passed"
echo "   🚀 GitHub: Pushed"

echo
info "🎯 Next Steps:"
echo "1. 🌐 Visit your repository: $REPO_URL"
echo "2. ⚙️ Configure repository settings:"
echo "   - Add repository description: 'Enterprise-ready Next.js 14 app with admin panel, dynamic tools, blog & security'"
echo "   - Add topics: nextjs, tailwindcss, typescript, admin-panel, tools, framer-motion, secure"
echo "   - Enable Issues and Discussions"
echo "3. 🔐 Add repository secrets for CI/CD:"
echo "   - VERCEL_TOKEN"
echo "   - VERCEL_ORG_ID"
echo "   - VERCEL_PROJECT_ID"
echo "4. 🚀 Deploy to your preferred platform:"
echo "   - Vercel: vercel --prod"
echo "   - VPS: ./scripts/deploy.sh production"
echo "5. 📊 Setup monitoring and analytics"
echo "6. 🔒 Configure domain and SSL certificates"

echo
echo -e "${PURPLE}📚 Documentation Available:${NC}"
echo "   📖 README.md - Project overview and setup"
echo "   🔐 docs/SECURITY.md - Security implementation guide"
echo "   ⚡ docs/PERFORMANCE.md - Performance optimization guide"
echo "   🚀 docs/DEPLOYMENT.md - Deployment instructions"
echo "   📝 docs/GIT_COMMIT_STRATEGY.md - Git workflow and commit standards"

echo
echo -e "${GREEN}🎉 Your enterprise-ready Next.js 14 project is now ready for development!${NC}"
echo -e "${BLUE}⭐ Don't forget to star the repository if you find it helpful!${NC}"
