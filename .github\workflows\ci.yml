# =============================================================================
# TOOLCRUSH - CI/CD PIPELINE
# =============================================================================
# Enterprise-grade GitHub Actions workflow for Next.js 14 application
# Includes: linting, type-checking, testing, security scanning, and building

name: 🚀 CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

# Concurrency control - cancel previous runs on new push
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  NODE_VERSION: '18'
  CACHE_KEY_PREFIX: 'toolcrush-v1'

jobs:
  # =============================================================================
  # CODE QUALITY & SECURITY
  # =============================================================================
  quality:
    name: 🔍 Code Quality & Security
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: |
          npm ci --prefer-offline --no-audit
          npm run download-fonts

      - name: 🔍 Run ESLint
        run: npm run lint

      - name: 🎨 Check code formatting
        run: npx prettier --check "src/**/*.{ts,tsx,js,jsx,json,css,md}"

      - name: 🔒 Security audit
        run: npm audit --audit-level=moderate

      - name: 📊 Upload ESLint results
        if: always()
        uses: github/super-linter@v4
        env:
          DEFAULT_BRANCH: main
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          VALIDATE_TYPESCRIPT_ES: true
          VALIDATE_JAVASCRIPT_ES: true

  # =============================================================================
  # TYPE CHECKING
  # =============================================================================
  typecheck:
    name: 🔧 TypeScript Check
    runs-on: ubuntu-latest
    timeout-minutes: 5

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: |
          npm ci --prefer-offline --no-audit
          npm run download-fonts

      - name: 🔧 Type check
        run: npx tsc --noEmit

  # =============================================================================
  # TESTING
  # =============================================================================
  test:
    name: 🧪 Tests
    runs-on: ubuntu-latest
    timeout-minutes: 15

    strategy:
      matrix:
        test-type: [unit, integration]

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: |
          npm ci --prefer-offline --no-audit
          npm run download-fonts

      - name: 🧪 Run tests
        run: npm run test:coverage
        env:
          NODE_ENV: test

      - name: 📊 Upload coverage to Codecov
        if: matrix.test-type == 'unit'
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

      - name: 📈 Coverage comment
        if: github.event_name == 'pull_request' && matrix.test-type == 'unit'
        uses: romeovs/lcov-reporter-action@v0.3.1
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          lcov-file: ./coverage/lcov.info

  # =============================================================================
  # BUILD & DEPLOYMENT
  # =============================================================================
  build:
    name: 🏗️ Build Application
    runs-on: ubuntu-latest
    needs: [quality, typecheck, test]
    timeout-minutes: 15

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: |
          npm ci --prefer-offline --no-audit
          npm run download-fonts

      - name: 🏗️ Build application
        run: npm run build
        env:
          NODE_ENV: production
          NEXT_TELEMETRY_DISABLED: 1

      - name: 📦 Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-files
          path: |
            .next/
            public/
          retention-days: 1

      - name: 📊 Analyze bundle size
        run: |
          npx @next/bundle-analyzer
          echo "Build completed successfully! 🎉"

  # =============================================================================
  # SECURITY SCANNING
  # =============================================================================
  security:
    name: 🛡️ Security Scan
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔒 Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: 📊 Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # =============================================================================
  # DEPLOYMENT (CONDITIONAL)
  # =============================================================================
  deploy-staging:
    name: 🚀 Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build, security]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    environment: staging
    timeout-minutes: 10

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-files

      - name: 🚀 Deploy to Vercel (Staging)
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./

  deploy-production:
    name: 🌟 Deploy to Production
    runs-on: ubuntu-latest
    needs: [build, security]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production
    timeout-minutes: 15

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-files

      - name: 🚀 Deploy to Vercel (Production)
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
          working-directory: ./

      - name: 📢 Deployment notification
        if: success()
        run: |
          echo "🎉 Production deployment successful!"
          echo "🌐 Application is live at: https://toolcrush.com"

  # =============================================================================
  # CLEANUP
  # =============================================================================
  cleanup:
    name: 🧹 Cleanup
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()

    steps:
      - name: 🧹 Clean up artifacts
        uses: geekyeggo/delete-artifact@v2
        with:
          name: build-files
          failOnError: false
