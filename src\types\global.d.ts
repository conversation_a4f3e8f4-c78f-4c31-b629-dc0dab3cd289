// global.d.ts or a separate bcrypt.d.ts
declare module 'bcrypt' {
    import * as bcrypt from 'bcryptjs'; // Or use `bcrypt` if that's what you're using
    export = bcrypt;
  }
  
  declare global {
    // For Mongoose
    var mongoose: {
      conn: mongoose.Connection | null;
      promise: Promise<mongoose.Connection> | null;
    };
    
    // For native MongoDB driver
    var mongoClient: {
      client: MongoClient | null;
      promise: Promise<MongoClient> | null;
    };
  }